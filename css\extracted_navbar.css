/* 
 * extracted_navbar.css - Lunfly 企业官网导航栏样式
 * 定义顶部导航栏的样式、动画和响应式行为
 * Synology 风格版本
 */

/* 导航栏基础样式 */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  background-color: var(--white);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  padding: 0.5rem 1rem;
  height: 76px;
}

.navbar-brand {
  display: flex;
  align-items: center;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
  text-decoration: none;
  margin-right: 2rem;
}

.navbar-brand:hover {
  color: var(--primary-color);
  text-decoration: none;
}

.navbar-brand img {
  height: 40px;
  margin-right: 0.5rem;
}

.navbar-nav {
  display: flex;
  align-items: center;
  margin: 0;
  padding: 0;
  list-style: none;
}

.nav-item {
  margin: 0 0.5rem;
  position: relative;
}

.nav-link {
  color: var(--gray-700);
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 1rem;
  transition: color 0.3s ease;
  display: block;
}

.nav-link:hover,
.nav-link.active {
  color: var(--primary-color);
  text-decoration: none;
}

/* 导航栏折叠按钮 */
.navbar-toggler {
  padding: 0.5rem;
  font-size: 1.25rem;
  line-height: 1;
  background-color: transparent;
  border: none;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.navbar-toggler:focus {
  outline: none;
  box-shadow: none;
}

.navbar-toggler i {
  font-size: 1.5rem;
  color: var(--gray-700);
  transition: all 0.3s ease;
}

/* 导航栏折叠内容 */
.navbar-collapse {
  flex-grow: 1;
  align-items: center;
}

/* 下拉菜单基础样式 */
.dropdown-menu {
  background-color: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: 4px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  padding: 0.5rem 0;
  margin-top: 0;
  display: none;
  min-width: 220px;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

/* 下拉菜单显示状态 */
.dropdown-menu.show {
  display: block;
  opacity: 1;
  visibility: visible;
}

/* 桌面端hover和点击效果 */
@media (min-width: 992px) {
  .navbar-expand-lg .navbar-nav {
    flex-direction: row;
  }

  .navbar-expand-lg .navbar-nav .dropdown-menu {
    position: absolute;
  }

  /* 一级下拉菜单悬停效果 */
  .dropdown:hover > .dropdown-menu {
    display: block;
    opacity: 1;
    visibility: visible;
  }
  
  /* 二级下拉菜单悬停效果 */
  .dropdown-submenu:hover > .dropdown-menu {
    display: block;
    opacity: 1;
    visibility: visible;
  }
  
  /* 二级菜单显示在右侧 */
  .dropdown-submenu > .dropdown-menu {
    top: 0;
    left: 100%;
    margin-top: -0.5rem;
    margin-left: 0;
  }
}

/* 下拉菜单项样式 */
.dropdown-item {
  color: var(--gray-700);
  font-weight: 500;
  font-size: 0.9rem;
  padding: 0.6rem 1.5rem;
  transition: all 0.2s ease;
  position: relative;
  white-space: nowrap;
  background-color: transparent;
}

.dropdown-item:hover,
.dropdown-item:focus,
.dropdown-item.active {
  background-color: rgba(0, 46, 93, 0.05);
  color: var(--primary-color);
}

/* 二级下拉菜单样式 */
.dropdown-submenu {
  position: relative;
}

.dropdown-submenu > .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.5em;
  vertical-align: middle;
  content: "";
  border-top: 0.3em solid transparent;
  border-right: 0;
  border-bottom: 0.3em solid transparent;
  border-left: 0.3em solid;
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
}

/* 移动端样式 */
@media (max-width: 991.98px) {
  .navbar {
    padding: 0.5rem 1rem;
  }

  .navbar-collapse {
    position: fixed;
    top: 76px;
    left: 0;
    right: 0;
    background-color: var(--white);
    padding: 1rem;
    max-height: calc(100vh - 76px);
    overflow-y: auto;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transform: translateY(-100%);
    transition: transform 0.3s ease;
  }

  .navbar-collapse.show {
    transform: translateY(0);
  }

  .navbar-nav {
    padding: 1rem 0;
  }

  .nav-item {
    margin: 0;
  }

  .nav-link {
    padding: 0.75rem 0;
  }

  .dropdown-menu {
    border: none;
    box-shadow: none;
    padding-left: 1rem;
    background-color: transparent;
    display: none;
    opacity: 1;
    visibility: visible;
  }

  .dropdown-menu.show {
    display: block;
  }

  .dropdown-item {
    padding: 0.75rem 0;
  }

  .dropdown-submenu > .dropdown-menu {
    margin-left: 1rem;
  }

  .dropdown-submenu > .dropdown-toggle::after {
    transform: rotate(90deg);
  }
}

/* 动画效果 */
@keyframes slideInDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 动画类 */
.animate-slideInDown {
  animation: slideInDown 0.5s ease forwards;
}

.animate-slideInLeft {
  animation: slideInLeft 0.5s ease forwards;
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease forwards;
}

/* 动画延迟 */
.animate-delay-1 { animation-delay: 0.1s; }
.animate-delay-2 { animation-delay: 0.2s; }
.animate-delay-3 { animation-delay: 0.3s; }
.animate-delay-4 { animation-delay: 0.4s; }

/* 初始隐藏状态 */
.initial-hidden {
  opacity: 0;
}

/* 社交链接 */
.social-links {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 10px;
}

.social-link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--white);
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.social-link:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-3px);
}
}