<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>节能效益计算器 - Lunfly工业智能解决方案</title>
  <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
  <link href="css/main.css" rel="stylesheet">
  <link href="css/extracted_navbar.css" rel="stylesheet">
  <link href="css/tool-item.css" rel="stylesheet">
  <link href="css/animations.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
</head>
<body>
  <!-- 顶部导航栏 -->
  <nav class="navbar navbar-expand-lg fixed-top" id="mainNav">
    <div class="container">
      <a class="navbar-brand initial-hidden animate-slideInLeft" href="index.html"><img src="images/logo.png" alt="Lunfly Logo" class="navbar-logo">Lunfly</a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarResponsive" aria-controls="navbarResponsive" aria-expanded="false" aria-label="Toggle navigation">
        <i class="bi bi-list"></i>
      </button>
      <div class="collapse navbar-collapse" id="navbarResponsive">
        <ul class="navbar-nav ms-auto">
          <!-- 产品与服务下拉菜单 -->
          <li class="nav-item dropdown initial-hidden animate-slideInLeft animate-delay-1">
            <a class="nav-link dropdown-toggle" href="#products" id="productsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
              产品与服务
            </a>
            <ul class="dropdown-menu" aria-labelledby="productsDropdown">
              <li class="dropdown-submenu">
                <a class="dropdown-item dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">控制系统</a>
                <ul class="dropdown-menu">
                  <li><a class="dropdown-item" href="webs_series.html">WEBs 系列</a></li>
                  <li><a class="dropdown-item" href="trend_series.html">Trend 系列</a></li>
                  <li><a class="dropdown-item" href="hotel_room_control.html">酒店客房控制</a></li>
                </ul>
              </li>
              <li class="dropdown-submenu">
                <a class="dropdown-item dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">智能设备</a>
                <ul class="dropdown-menu">
                  <li><a class="dropdown-item" href="valve_actuator.html">阀门与执行器</a></li>
                  <li><a class="dropdown-item" href="sensor.html">传感器</a></li>
                  <li><a class="dropdown-item" href="thermostat.html">温控器</a></li>
                </ul>
              </li>
              <li class="dropdown-submenu">
                <a class="dropdown-item dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">技术服务</a>
                <ul class="dropdown-menu">
                  <li><a class="dropdown-item" href="system_integration.html">系统集成</a></li>
                  <li><a class="dropdown-item" href="technical_consulting.html">技术咨询</a></li>
                  <li><a class="dropdown-item" href="operation_maintenance.html">运维服务</a></li>
                </ul>
              </li>
            </ul>
          </li>

          <!-- 解决方案下拉菜单 -->
          <li class="nav-item dropdown initial-hidden animate-slideInLeft animate-delay-2">
            <a class="nav-link dropdown-toggle" href="#solutions" id="solutionsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
              解决方案
            </a>
            <ul class="dropdown-menu" aria-labelledby="solutionsDropdown">
              <li class="dropdown-submenu">
                <a class="dropdown-item dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">楼宇自控</a>
                <ul class="dropdown-menu">
                  <li><a class="dropdown-item" href="commercial.html">商业楼宇</a></li>
                  <li><a class="dropdown-item" href="hospital.html">医院</a></li>
                  <li><a class="dropdown-item" href="transportation.html">轨道交通</a></li>
                </ul>
              </li>
              <li class="dropdown-submenu">
                <a class="dropdown-item dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">节能改造</a>
                <ul class="dropdown-menu">
                  <li><a class="dropdown-item" href="heat-recovery.html">余热回收</a></li>
                  <li><a class="dropdown-item" href="alternative-energy.html">替代能源</a></li>
                </ul>
              </li>
              <li class="dropdown-submenu">
                <a class="dropdown-item dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">智慧农业</a>
                <ul class="dropdown-menu">
                  <li><a class="dropdown-item" href="environment-control.html">环境调控</a></li>
                  <li><a class="dropdown-item" href="data-collection.html">环境监测</a></li>
                </ul>
              </li>
            </ul>
          </li>

          <!-- 技术支持下拉菜单 -->
          <li class="nav-item dropdown initial-hidden animate-slideInLeft animate-delay-3">
            <a class="nav-link dropdown-toggle" href="#support" id="supportDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
              技术支持
            </a>
            <ul class="dropdown-menu" aria-labelledby="supportDropdown">
              <li><a class="dropdown-item" href="online-service.html">在线报修</a></li>
              <li><a class="dropdown-item" href="remote-diagnosis.html">远程诊断</a></li>
            </ul>
          </li>

          <!-- 关于我们下拉菜单 -->
          <li class="nav-item dropdown initial-hidden animate-slideInLeft animate-delay-4">
            <a class="nav-link dropdown-toggle" href="#about" id="aboutDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
              关于我们
            </a>
            <ul class="dropdown-menu" aria-labelledby="aboutDropdown">
              <li><a class="dropdown-item" href="history.html">发展历程</a></li>
              <li><a class="dropdown-item" href="contact.html">联系方式</a></li>
              <li><a class="dropdown-item" href="recruitment.html">伦飞招聘</a></li>
            </ul>
          </li>
        </ul>
      </div>
    </div>
  </nav>

  <!-- 页面标题 -->
  <section class="section">
    <div class="container">
      <div class="row">
        <div class="col-12">
          <h1 class="text-center mb-4">节能效益计算器</h1>
          <p class="text-center lead">评估楼宇自控系统节能改造的投资回报</p>
        </div>
      </div>
    </div>
  </section>

  <!-- 计算器主体 -->
  <section class="section bg-light">
    <div class="container">
      <div class="row">
        <div class="col-lg-8 mx-auto">
          <div class="card">
            <div class="card-header">
              <h3 class="mb-0">
                <i class="bi bi-calculator text-primary me-2"></i>节能效益计算
              </h3>
            </div>
            <div class="card-body">
              <form id="energyCalculatorForm">
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label for="buildingArea" class="form-label">建筑面积 (平方米)</label>
                    <input type="number" class="form-control" id="buildingArea" placeholder="请输入建筑面积" required>
                  </div>
                  <div class="col-md-6 mb-3">
                    <label for="currentEnergyCost" class="form-label">年度能源费用 (万元)</label>
                    <input type="number" class="form-control" id="currentEnergyCost" placeholder="请输入当前年度能源费用" step="0.1" required>
                  </div>
                </div>

                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label for="buildingType" class="form-label">建筑类型</label>
                    <select class="form-select" id="buildingType" required>
                      <option value="">请选择建筑类型</option>
                      <option value="office">办公楼</option>
                      <option value="hotel">酒店</option>
                      <option value="hospital">医院</option>
                      <option value="mall">商场</option>
                      <option value="school">学校</option>
                      <option value="factory">工厂</option>
                    </select>
                  </div>
                  <div class="col-md-6 mb-3">
                    <label for="systemAge" class="form-label">现有系统使用年限</label>
                    <select class="form-select" id="systemAge" required>
                      <option value="">请选择使用年限</option>
                      <option value="new">5年以下</option>
                      <option value="medium">5-10年</option>
                      <option value="old">10年以上</option>
                    </select>
                  </div>
                </div>

                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label for="investmentAmount" class="form-label">预计投资金额 (万元)</label>
                    <input type="number" class="form-control" id="investmentAmount" placeholder="请输入预计投资金额" step="0.1" required>
                  </div>
                  <div class="col-md-6 mb-3">
                    <label for="electricityPrice" class="form-label">电价 (元/度)</label>
                    <input type="number" class="form-control" id="electricityPrice" placeholder="请输入电价" step="0.01" value="0.8" required>
                  </div>
                </div>

                <div class="text-center">
                  <button type="submit" class="btn btn-primary btn-lg">
                    <i class="bi bi-calculator me-2"></i>计算节能效益
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- 计算结果 -->
  <section class="section" id="resultSection" style="display: none;">
    <div class="container">
      <div class="row">
        <div class="col-lg-8 mx-auto">
          <div class="card border-success">
            <div class="card-header bg-success text-white">
              <h3 class="mb-0">
                <i class="bi bi-graph-up me-2"></i>计算结果
              </h3>
            </div>
            <div class="card-body">
              <div class="row text-center">
                <div class="col-md-3 mb-3">
                  <div class="border rounded p-3">
                    <h5 class="text-primary">预计节能率</h5>
                    <h3 id="energySavingRate" class="text-success">--</h3>
                  </div>
                </div>
                <div class="col-md-3 mb-3">
                  <div class="border rounded p-3">
                    <h5 class="text-primary">年节约费用</h5>
                    <h3 id="annualSavings" class="text-success">--</h3>
                  </div>
                </div>
                <div class="col-md-3 mb-3">
                  <div class="border rounded p-3">
                    <h5 class="text-primary">投资回收期</h5>
                    <h3 id="paybackPeriod" class="text-warning">--</h3>
                  </div>
                </div>
                <div class="col-md-3 mb-3">
                  <div class="border rounded p-3">
                    <h5 class="text-primary">10年净收益</h5>
                    <h3 id="tenYearProfit" class="text-info">--</h3>
                  </div>
                </div>
              </div>

              <div class="mt-4">
                <h5>节能分析说明：</h5>
                <div id="analysisText" class="alert alert-info">
                  <!-- 分析结果将在这里显示 -->
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>



  <!-- 页脚 -->
  <footer class="footer bg-dark py-5">
    <div class="container">
      <div class="row">
        <div class="col-lg-3 mb-4">
          <h5 class="mb-4">关于我们</h5>
          <ul class="list-unstyled">
            <li><a href="company_profile.html">公司简介</a></li>
            <li><a href="history.html">发展历程</a></li>
            <li><a href="team.html">团队介绍</a></li>
            <li><a href="culture.html">企业文化</a></li>
          </ul>
        </div>
        <div class="col-lg-3 mb-4">
          <h5 class="mb-4">产品与服务</h5>
          <ul class="list-unstyled">
            <li><a href="intelligent_control.html">智能控制系统</a></li>
            <li><a href="energy_management.html">能源管理平台</a></li>
            <li><a href="agriculture_monitoring.html">农业智能监测</a></li>
            <li><a href="data_analysis.html">数据采集监控</a></li>
          </ul>
        </div>
        <div class="col-lg-3 mb-4">
          <h5 class="mb-4">解决方案</h5>
          <ul class="list-unstyled">
            <li><a href="building_automation.html">楼宇自动化</a></li>
            <li><a href="energy_optimization.html">能源优化</a></li>
            <li><a href="smart_agriculture.html">智慧农业</a></li>
            <li><a href="system_integration.html">系统集成</a></li>
          </ul>
        </div>
        <div class="col-lg-3">
          <h5 class="mb-4">联系我们</h5>
          <ul class="list-unstyled">
            <li><i class="bi bi-geo-alt me-2"></i>地址: 北京市朝阳区建国路88号</li>
            <li><i class="bi bi-telephone me-2"></i>电话: 010-12345678</li>
            <li><i class="bi bi-envelope me-2"></i>邮箱: <EMAIL></li>
          </ul>
        </div>
      </div>
      <hr class="my-4">
      <div class="row">
        <div class="col-12 text-center small">
          版权所有 ©2025 北京伦飞天成科贸有限公司 | 京公网安备 123456789123456号 | 京ICP备12345678号 | <a href="#">条款条约</a> | <a href="#">隐私声明</a> | Honeywell授权代理商
        </div>
      </div>
    </div>
  </footer>

  <!-- Scripts -->
  <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
  <script src="js/main.js"></script>
  <script>
    document.getElementById('energyCalculatorForm').addEventListener('submit', function(e) {
      e.preventDefault();

      // 获取表单数据
      const buildingArea = parseFloat(document.getElementById('buildingArea').value);
      const currentEnergyCost = parseFloat(document.getElementById('currentEnergyCost').value);
      const buildingType = document.getElementById('buildingType').value;
      const systemAge = document.getElementById('systemAge').value;
      const investmentAmount = parseFloat(document.getElementById('investmentAmount').value);
      const electricityPrice = parseFloat(document.getElementById('electricityPrice').value);

      // 根据建筑类型确定基础节能率
      let baseSavingRate = 0;
      switch(buildingType) {
        case 'office': baseSavingRate = 0.20; break;
        case 'hotel': baseSavingRate = 0.25; break;
        case 'hospital': baseSavingRate = 0.18; break;
        case 'mall': baseSavingRate = 0.22; break;
        case 'school': baseSavingRate = 0.15; break;
        case 'factory': baseSavingRate = 0.30; break;
        default: baseSavingRate = 0.20;
      }

      // 根据系统年限调整节能率
      let ageMultiplier = 1;
      switch(systemAge) {
        case 'new': ageMultiplier = 0.8; break;
        case 'medium': ageMultiplier = 1.0; break;
        case 'old': ageMultiplier = 1.3; break;
      }

      // 计算最终节能率
      const finalSavingRate = Math.min(baseSavingRate * ageMultiplier, 0.45);

      // 计算年节约费用
      const annualSavings = currentEnergyCost * finalSavingRate;

      // 计算投资回收期
      const paybackPeriod = investmentAmount / annualSavings;

      // 计算10年净收益
      const tenYearProfit = (annualSavings * 10) - investmentAmount;

      // 显示结果
      document.getElementById('energySavingRate').textContent = (finalSavingRate * 100).toFixed(1) + '%';
      document.getElementById('annualSavings').textContent = annualSavings.toFixed(1) + '万元';
      document.getElementById('paybackPeriod').textContent = paybackPeriod.toFixed(1) + '年';
      document.getElementById('tenYearProfit').textContent = tenYearProfit.toFixed(1) + '万元';

      // 生成分析说明
      let analysisText = `根据您提供的信息，${buildingArea}平方米的${getBuildingTypeName(buildingType)}`;
      analysisText += `通过楼宇自控系统改造，预计可实现${(finalSavingRate * 100).toFixed(1)}%的节能效果。`;
      analysisText += `年节约能源费用${annualSavings.toFixed(1)}万元，投资回收期约${paybackPeriod.toFixed(1)}年。`;

      if (paybackPeriod <= 3) {
        analysisText += ' 投资回收期较短，经济效益显著，建议实施改造。';
      } else if (paybackPeriod <= 5) {
        analysisText += ' 投资回收期适中，具有良好的经济效益。';
      } else {
        analysisText += ' 投资回收期较长，建议进一步优化改造方案。';
      }

      document.getElementById('analysisText').textContent = analysisText;

      // 显示结果区域
      document.getElementById('resultSection').style.display = 'block';
      document.getElementById('resultSection').scrollIntoView({ behavior: 'smooth' });
    });

    function getBuildingTypeName(type) {
      const typeNames = {
        'office': '办公楼',
        'hotel': '酒店',
        'hospital': '医院',
        'mall': '商场',
        'school': '学校',
        'factory': '工厂'
      };
      return typeNames[type] || '建筑';
    }
  </script>
</body>
</html>