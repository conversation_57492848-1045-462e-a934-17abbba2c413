document.addEventListener('DOMContentLoaded', function() {
  // 观察功能区块
  const featureObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('visible');
        featureObserver.unobserve(entry.target);
      }
    });
  }, {
    threshold: 0.2,
    rootMargin: '0px 0px -100px 0px'
  });

  // 观察所有功能区块
  document.querySelectorAll('.feature-block').forEach(block => {
    featureObserver.observe(block);
  });

  // 处理二级菜单
  const dropdownSubmenus = document.querySelectorAll('.dropdown-submenu');
  
  dropdownSubmenus.forEach(submenu => {
    const dropdownToggle = submenu.querySelector('.dropdown-item.dropdown-toggle');
    const dropdownMenu = submenu.querySelector('.dropdown-menu');
    
    // 移动端处理
    if (window.innerWidth < 992) {
      dropdownToggle.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        // 关闭其他打开的子菜单
        dropdownSubmenus.forEach(other => {
          if (other !== submenu) {
            other.querySelector('.dropdown-menu').style.display = 'none';
          }
        });
        
        // 切换当前子菜单
        const isDisplayed = dropdownMenu.style.display === 'block';
        dropdownMenu.style.display = isDisplayed ? 'none' : 'block';
      });
    } else {
      // 桌面端处理
      submenu.addEventListener('mouseenter', function() {
        dropdownMenu.style.display = 'block';
      });
      
      submenu.addEventListener('mouseleave', function() {
        dropdownMenu.style.display = 'none';
      });
    }
  });
  
  // 处理一级菜单在移动端的点击
  const dropdowns = document.querySelectorAll('.nav-item.dropdown');
  dropdowns.forEach(dropdown => {
    const dropdownToggle = dropdown.querySelector('.dropdown-toggle');
    const dropdownMenu = dropdown.querySelector('.dropdown-menu');
    
    if (window.innerWidth < 992) {
      dropdownToggle.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        // 关闭其他打开的菜单
        dropdowns.forEach(other => {
          if (other !== dropdown) {
            other.querySelector('.dropdown-menu').classList.remove('show');
          }
        });
        
        // 切换当前菜单
        dropdownMenu.classList.toggle('show');
      });
    }
  });
  
  // 监听窗口大小变化，重新初始化菜单行为
  window.addEventListener('resize', function() {
    const isMobile = window.innerWidth < 992;
    
    dropdownSubmenus.forEach(submenu => {
      const dropdownMenu = submenu.querySelector('.dropdown-menu');
      dropdownMenu.style.display = '';
    });
    
    dropdowns.forEach(dropdown => {
      const dropdownMenu = dropdown.querySelector('.dropdown-menu');
      dropdownMenu.classList.remove('show');
    });
  });
}); 