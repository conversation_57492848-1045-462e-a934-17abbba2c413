/* 
 * main.css - Lunfly 企业官网主样式表
 * Synology 风格版本
 */

:root {
  --primary-color: #0b76ce;
  --main-color: #0b76ce;       /* 添加main-color变量，与primary-color保持一致 */
  --secondary-color: #2c3e50;
  --accent-color: #3498db;
  --text-color: #333;
  --light-bg: #f8f9fa;
  --dark-bg: #2c3e50;
  --dark-blue: #003366;        /* 深蓝色 */
  --light-bg: #f5f5f5;         /* 浅灰背景色 */
  --gray-light: #e6e6e6;
  --gray: #6c757d;
  --dark: #212529;
  --white: #fff;
}

/* 基础样式 */
body {
  font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: var(--text-color);
  overflow-x: hidden;
  scroll-behavior: smooth;
  background-color: var(--white);
  line-height: 1.6;
  padding-top: 76px; /* 添加顶部padding，为固定导航栏留出空间 */
}

h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-color);
}

h2 {
  font-size: 2rem;
  margin-bottom: 1.5rem;
}

section {
  overflow: hidden;
}

.section {
  padding: 70px 0;
  background-color: var(--white);
}

.section-gray {
  background-color: var(--light-bg);
}

@media (max-width: 768px) {
  .section {
    padding: 50px 0;
  }
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: all 0.3s ease;
}

a:hover {
  color: var(--dark-blue);
  text-decoration: none;
}

/* 按钮样式 */
.btn {
  border-radius: 4px;
  padding: 8px 24px;
  font-weight: 500;
  transition: all 0.3s ease;
  text-transform: none;
  letter-spacing: normal;
}

.btn-sm {
  padding: 6px 15px;
  font-size: 0.875rem;
}

.btn-dark {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--white);
}

.btn-dark:hover,
.btn-dark:focus {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
  transform: translateY(-2px);
}

.btn-outline-dark {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-outline-dark:hover {
  background-color: var(--primary-color);
  color: var(--white);
  transform: translateY(-2px);
}

/* Hero Section */
.hero {
  position: relative;
  min-height: 66vh;
  padding: 0;
  overflow: hidden;
  margin-bottom: 0;
}

.carousel-item {
  min-height: 66vh;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
}

.carousel-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.4));
  z-index: 1;
}

.carousel-item .container {
  position: relative;
  z-index: 2;
}

.hero-content {
  color: #ffffff;
  padding: 0;
  margin-top: 25vh;
  text-align: left;
  margin-left: 10%;
}

.hero-title {
  font-size: 2.75rem;
  font-weight: 700;
  margin-bottom: 1.1rem;
  color: #ffffff;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
  font-size: 1.2rem;
  margin-bottom: 1.65rem;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.hero-btn {
  padding: 11px 22px;
  font-size: 1.1rem;
  background-color: var(--primary-color);
  border: 2px solid var(--primary-color);
  color: #ffffff;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.hero-btn:hover {
  background-color: transparent;
  color: #ffffff;
  border-color: #ffffff;
}

.hero-image {
  position: relative;
  z-index: 4;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem 0 2rem 2rem;
  visibility: visible;
}

.hero-image .app-interface {
  width: 100%;
  max-width: 600px;
  box-shadow: 0 15px 30px rgba(0,0,0,0.1);
  border-radius: 8px;
  overflow: hidden;
}

.hero-indicators {
  position: absolute;
  bottom: 180px; /* 调整位置，更靠近底部 */
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  gap: 10px;
  z-index: 3;
}

.hero-indicators .indicator {
  width: 14px; /* 增加大小 */
  height: 14px; /* 增加大小 */
  border-radius: 50%;
  background-color: rgba(255,255,255,0.7); /* 提高不活跃指示器的对比度 */
  border: none;
  padding: 0;
  margin: 0 5px;
  transition: all 0.3s ease;
  cursor: pointer; /* 添加指针样式提示可点击 */
}

.hero-indicators .indicator:hover {
  background-color: rgba(255,255,255,0.9); /* 悬停效果 */
  transform: scale(1.1);
}

.hero-indicators .indicator.active {
  background-color: var(--primary-color);
  transform: scale(1.2);
  box-shadow: 0 0 5px rgba(0,0,0,0.2); /* 为活跃指示器添加阴影 */
}

@media (max-width: 1199.98px) {
  .hero-title {
    font-size: 2.8rem;
  }
  
  .hero-subtitle {
    font-size: 1.4rem;
  }
}

@media (max-width: 991.98px) {
  .hero {
    height: auto;
    min-height: auto;
    padding: 80px 0 60px;
  }
  
  .hero-content {
    text-align: center;
    padding: 2rem 1.5rem 3rem;
    align-items: center;
    z-index: 5;
    visibility: visible;
  }
  
  .hero-subtitle {
    max-width: 100%;
  }
  
  .hero-btn {
    align-self: center;
  }
  
  .hero-image {
    padding: 0 1.5rem 2.5rem;
    z-index: 4;
    visibility: visible;
  }
  
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
  }
  
  .hero .carousel-item {
    position: relative;
    z-index: 1;
  }
}

@media (max-width: 767.98px) {
  .hero {
    padding: 70px 0 50px;
    overflow: visible;
  }
  
  .hero-content {
    padding: 1.5rem 1rem 2rem;
    z-index: 5;
    visibility: visible;
  }
  
  .hero-title {
    font-size: 2.2rem;
  }
  
  .hero-image {
    padding: 0 1rem 2rem;
    z-index: 4;
    visibility: visible;
  }
  
  .hero-indicators {
    bottom: 10px;
    z-index: 6;
  }
  
  .hero-indicators .indicator {
    width: 10px;
    height: 10px;
  }
}

.app-interface {
  background: white;
  border-radius: 12px;
  padding: 1.25rem;
  width: 100%;
  max-width: 550px;
  margin: 0 auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease;
}

.app-interface img {
  border-radius: 8px;
  width: 100%;
  height: auto;
}

/* Carousel Controls */
.carousel-control-prev,
.carousel-control-next {
  width: 5%;
  opacity: 0.8;
  transition: all 0.3s ease;
  background: rgba(0, 0, 0, 0.1);
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
  opacity: 1;
  background: rgba(0, 0, 0, 0.2);
}

.hero-indicators {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 15;
  display: flex;
  gap: 15px;
}

.indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  border: 2px solid rgba(255, 255, 255, 0.7);
  transition: all 0.3s ease;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.indicator:hover {
  background-color: rgba(255, 255, 255, 0.8);
  transform: scale(1.1);
}

.indicator.active {
  background-color: var(--primary-color);
  border-color: white;
  transform: scale(1.2);
}

/* Responsive Design */
@media (max-width: 992px) {
  .hero {
    height: auto;
    min-height: 500px;
    padding: 80px 0;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.2rem;
  }

  .hero-content {
    text-align: center;
    margin-bottom: 2rem;
  }

  .hero-image {
    margin-top: 2rem;
  }
}

@media (max-width: 576px) {
  .hero {
    padding: 60px 0;
    min-height: 450px;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .hero-indicators {
    bottom: 1.5rem;
  }
}

/* Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-content {
  animation: fadeIn 1s ease-out;
}

/* 页面头部 */
.page-header {
  position: relative;
  height: 300px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: var(--white);
  margin-bottom: 3rem;
  padding: 4rem 2rem;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1;
}

.page-header .container {
  position: relative;
  z-index: 2;
}

.page-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--white);
}

.page-header p {
  font-size: 1.2rem;
  max-width: 800px;
  margin: 0 auto;
  color: var(--white);
}

@media (max-width: 768px) {
  .page-header {
    height: 30vh;
    min-height: 250px;
  }
  
  .page-header h1 {
    font-size: 2.25rem;
  }
  
  .page-header p {
    font-size: 1rem;
  }
  
  .contact-info-item {
    margin-bottom: 1.5rem;
  }
  
  .contact-icon {
    width: 40px;
    height: 40px;
    font-size: 1.25rem;
  }
  
  .map-container {
    height: 300px;
    margin-bottom: 2rem;
  }
  
  .office-card img {
    height: 150px;
  }
}

/* 卡片样式 */
.card {
  border: none;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.07);
  transition: all 0.3s ease;
  margin-bottom: 20px;
  overflow: hidden;
  background-color: var(--white);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.card-title {
  font-weight: 600;
  margin-bottom: 15px;
  color: var(--text-color);
}

.product-card {
  text-align: center;
  padding: 15px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.product-card .card-body {
  padding: 25px;
  flex-grow: 1;
}

.product-card .card-footer {
  background-color: transparent;
  border-top: none;
  padding-top: 0;
}

/* 图标样式 */
.icon-large {
  font-size: 3rem;
  color: var(--primary-color);
  margin-bottom: 20px;
  display: block;
}

.icon-medium {
  font-size: 2rem;
  color: var(--primary-color);
  margin-bottom: 15px;
  display: block;
}

/* 解决方案项目样式 */
.solution-item {
  transition: all 0.3s ease;
  margin-bottom: 30px;
}

.solution-img-container {
  overflow: hidden;
  border-radius: 8px;
}

.solution-img-container img {
  transition: transform 0.5s ease;
  width: 100%;
}

.solution-item:hover img {
  transform: scale(1.05);
}

/* 技术支持项目样式 */
.support-item {
  padding: 20px;
  text-align: center;
  transition: all 0.3s ease;
  height: 100%;
  background-color: var(--white);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.07);
}

.support-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

/* 联系信息样式 */
.contact-info {
  margin-bottom: 30px;
}

.contact-info p {
  margin-bottom: 15px;
  font-size: 1.1rem;
}

.contact-info i {
  color: var(--primary-color);
  margin-right: 10px;
  width: 20px;
  text-align: center;
}

/* 页脚样式 */
.footer {
  background-color: #212529;
  color: #ffffff;
  padding: 4rem 0 2rem;
  position: relative;
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #009999 0%, #00a0a0 50%, #00a8a8 100%);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
  margin-bottom: 3rem;
}

.footer-section h4 {
  color: #ffffff;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  position: relative;
  padding-bottom: 0.5rem;
}

.footer-section h4::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 40px;
  height: 2px;
  background-color: #00a8a8;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 0.8rem;
}

.footer-links a {
  color: #cccccc;
  text-decoration: none;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.footer-links a:hover {
  color: #00a8a8;
  padding-left: 5px;
}

.footer-contact {
  margin-top: 1.5rem;
}

.footer-contact p {
  color: #cccccc;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
}

.footer-contact i {
  margin-right: 0.8rem;
  color: #00a8a8;
  width: 20px;
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 2rem;
  margin-top: 2rem;
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-copyright {
  display: flex;
  justify-content: center;
  align-items: center;
  color: #999999;
  font-size: 0.85rem;
  text-align: center;
  padding: 0 15px;
}

.footer-copyright a {
  color: #999999;
  text-decoration: none;
  transition: all 0.3s ease;
}

.footer-copyright a:hover {
  color: #00a8a8;
}

.footer-copyright > * {
  margin: 0 8px;
}

.footer-social {
  display: flex;
  gap: 1rem;
}

.footer-social a {
  color: #cccccc;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.footer-social a:hover {
  color: #00a8a8;
  transform: translateY(-2px);
}

/* Responsive Footer */
@media (max-width: 992px) {
  .footer-content {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 576px) {
  .footer {
    padding: 3rem 0 1.5rem;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 2.5rem;
  }

  .footer-bottom-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .footer-social {
    justify-content: center;
  }
}

/* 时间线样式 */
.timeline {
  position: relative;
  padding: 2rem 0;
}

.timeline::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 2px;
  height: 100%;
  background-color: var(--gray-light);
}

.timeline-item {
  position: relative;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
}

.timeline-badge {
  width: 80px;
  height: 80px;
  background-color: var(--primary-color);
  color: var(--white);
  border-radius: 50%;
  font-weight: 700;
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
  box-shadow: 0 5px 15px rgba(11, 118, 206, 0.2);
  position: relative;
  margin: 0 2rem;
}

.timeline-content {
  background-color: var(--white);
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.07);
  flex: 1;
  position: relative;
  max-width: calc(50% - 5rem);
}

.timeline-content h5 {
  margin-top: 0;
  color: var(--primary-color);
}

.timeline-content p {
  margin-bottom: 0;
}

.timeline-item:nth-child(odd) {
  justify-content: flex-end;
}

.timeline-item:nth-child(even) {
  justify-content: flex-start;
}

@media (max-width: 768px) {
  .timeline::before {
    left: 40px;
  }
  
  .timeline-item {
    justify-content: flex-start;
  }
  
  .timeline-badge {
    width: 60px;
    height: 60px;
    font-size: 1rem;
    margin: 0 1rem 0 0;
  }
  
  .timeline-content {
    max-width: calc(100% - 80px);
  }
}

/* 表单样式 */
.form-control {
  border-radius: 4px;
  padding: 0.75rem 1rem;
  border: 1px solid #dee2e6;
}

.form-control:focus {
  box-shadow: 0 0 0 0.25rem rgba(11, 118, 206, 0.25);
  border-color: var(--primary-color);
}

.form-select {
  border-radius: 4px;
  padding: 0.75rem 1rem;
  border: 1px solid #dee2e6;
}

.form-select:focus {
  box-shadow: 0 0 0 0.25rem rgba(11, 118, 206, 0.25);
  border-color: var(--primary-color);
}

/* 导航栏样式 */
#mainNav {
  background-color: var(--white);
  transition: all 0.3s ease;
  padding: 0.5rem 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

#mainNav .navbar-brand {
  color: var(--text-color);
  font-weight: 600;
}

#mainNav .nav-link {
  color: var(--text-color);
  font-weight: 500;
  padding: 0.5rem 1rem;
}

#mainNav .nav-link:hover {
  color: var(--primary-color);
}

#mainNav .nav-link.active {
  color: var(--primary-color);
}

/* 社交链接 */
.social-links {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.social-link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--white);
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.social-link:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-3px);
}

/* 楼宇自控页面 - 功能模块样式 */
.function-item {
  background-color: var(--white);
  border-radius: 8px;
  padding: 25px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.07);
  height: 100%;
  transition: all 0.3s ease;
}

.function-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.function-icon {
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: 15px;
}

.function-item h4 {
  font-weight: 600;
  margin-bottom: 15px;
}

.architecture-item {
  background-color: var(--white);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.07);
  margin-bottom: 20px;
  transition: all 0.3s ease;
}

.architecture-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.architecture-item h4 {
  color: var(--primary-color);
  font-weight: 600;
  margin-bottom: 10px;
}

/* 流程时间线 */
.process-timeline {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
}

.process-timeline::before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 50px;
  width: 3px;
  background-color: var(--primary-color);
}

.process-item {
  position: relative;
  margin-bottom: 30px;
  padding-left: 100px;
}

.process-number {
  position: absolute;
  left: 30px;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  background-color: var(--primary-color);
  color: var(--white);
  font-weight: bold;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.process-content {
  background-color: var(--white);
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.07);
}

.process-content h4 {
  color: var(--primary-color);
  margin-bottom: 10px;
  font-weight: 600;
}

/* 特性项目样式 */
.feature-item {
  background-color: var(--white);
  border-radius: 8px;
  padding: 25px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.07);
  height: 100%;
  transition: all 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.feature-item h4 {
  color: var(--primary-color);
  margin-bottom: 15px;
  font-weight: 600;
}

.feature-item ul {
  padding-left: 20px;
}

.feature-item ul li {
  margin-bottom: 10px;
}

@media (max-width: 768px) {
  .process-timeline::before {
    left: 30px;
  }
  
  .process-item {
    padding-left: 70px;
  }
  
  .process-number {
    left: 20px;
    width: 30px;
    height: 30px;
    font-size: 0.9rem;
  }
  
  h2 {
    font-size: 1.8rem;
  }
}

/* 响应式调整 */
@media (max-width: 992px) {
  .hero {
    height: auto;
    min-height: 500px;
    padding: 80px 0 40px;
  }
  
  .hero-title {
    font-size: 2.8rem;
  }
  
  .hero-subtitle {
    font-size: 1.3rem;
  }
  
  .app-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .hero-content {
    text-align: center;
    margin-bottom: 2rem;
    padding-left: 0;
  }
  
  .hero-image {
    padding: 0 15px;
  }
  
  .carousel-control-prev,
  .carousel-control-next {
    width: 10%;
  }
}

@media (max-width: 576px) {
  .hero {
    padding: 100px 0 80px;
    min-height: 450px;
  }
  
  .hero-title {
    font-size: 2.2rem;
  }
  
  .hero-subtitle {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
  }
  
  .hero-indicators {
    bottom: 15px;
  }
  
  .app-icon img {
    width: 40px;
    height: 40px;
  }
  
  .app-icon span {
    font-size: 0.7rem;
  }
  
  .carousel-control-prev,
  .carousel-control-next {
    width: 8%;
  }
}

/* 功能区块 */
.feature-blocks {
  background: linear-gradient(135deg, rgba(230, 255, 230, 0.5) 0%, rgba(255, 230, 230, 0.5) 50%, rgba(230, 230, 255, 0.5) 100%);
  padding: 0;
  position: relative;
  overflow: hidden;
  margin-top: 0;
}

.feature-blocks::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  backdrop-filter: blur(50px);
  z-index: 0;
}

.feature-blocks .row {
  position: relative;
  z-index: 1;
}

.feature-block {
  position: relative;
  padding: 40px;
  border-radius: 10px;
  height: 100%;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  color: #fff;
  opacity: 0;
  transform: translateY(30px);
}

.feature-block.visible {
  opacity: 1;
  transform: translateY(0);
}

.feature-block:nth-child(1) {
  transition-delay: 0.1s;
}

.feature-block:nth-child(2) {
  transition-delay: 0.2s;
}

.feature-block:nth-child(3) {
  transition-delay: 0.3s;
}

.feature-block:nth-child(4) {
  transition-delay: 0.4s;
}

.feature-block::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.7));
  z-index: 1;
}

.feature-content {
  position: relative;
  z-index: 2;
}

.feature-block img {
  display: none;
}

.feature-block h3 {
  font-size: 1.6rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: #fff;
  line-height: 1.3;
  transition: all 0.3s ease;
}

.feature-block:hover {
  transform: translateY(-5px);
}

.feature-block:hover h3 {
  transform: translateY(-5px);
}

.feature-desc {
  font-size: 0.95rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 1rem;
  line-height: 1.5;
  max-height: none;
  opacity: 1;
  transform: none;
}

.feature-link {
  color: #fff;
  font-size: 0.9rem;
  font-weight: 500;
  position: relative;
  opacity: 1;
  transform: none;
  display: inline-flex;
  align-items: center;
  text-decoration: none;
  margin-top: 1rem;
}

.feature-link i {
  margin-left: 0.5rem;
  transition: transform 0.3s ease;
}

.feature-link:hover {
  color: #fff;
}

.feature-link:hover i {
  transform: translateX(3px);
}

/* 背景图片 */
.dark-server {
  background-image: url('https://via.placeholder.com/800x600/102030/ffffff?text=Server+Room');
}

.office-scene {
  background-image: url('https://via.placeholder.com/800x600/203040/ffffff?text=Office');
}

.skyworth {
  background-image: url('https://via.placeholder.com/800x600/304050/ffffff?text=Skyworth');
}

.device-sync {
  background-image: url('https://via.placeholder.com/800x600/102030/ffffff?text=Device+Sync');
}

@media (max-width: 992px) {
  .feature-block {
    height: 280px;
  }
  
  .feature-desc {
    opacity: 1;
    transform: translateY(0);
    margin-bottom: 1.5rem;
  }
  
  .feature-link {
    opacity: 1;
    transform: translateY(0);
    position: relative;
    bottom: auto;
    left: auto;
    display: inline-flex;
    margin-top: 0.5rem;
  }
  
  .feature-block:hover .feature-content {
    padding-bottom: 2rem;
  }
}

@media (max-width: 768px) {
  .feature-blocks .row {
    display: flex;
    flex-wrap: wrap;
  }
  
  .feature-blocks .col-md-3 {
    width: 50%;
  }
  
  .feature-block {
    height: 250px;
  }
  
  .feature-block h3 {
    font-size: 1.3rem;
  }
  
  .feature-content {
    padding: 1.5rem;
  }
}

@media (max-width: 576px) {
  .feature-blocks .col-md-3 {
    width: 100%;
  }
  
  .feature-block {
    height: 200px;
  }
  
  .feature-block h3 {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
  }
  
  .feature-desc {
    font-size: 0.85rem;
    max-height: 2.5rem;
  }
  
  .feature-content {
    padding: 1.25rem;
  }
  
  .feature-link {
    font-size: 0.85rem;
  }
}

/* 工具区域 */
.tools-section {
  background-color: #ffffff;
  padding: 80px 0;
  position: relative;
}

.tools-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 120, 212, 0.05), rgba(0, 120, 212, 0.1));
  z-index: 0;
}

.tools-section .container {
  position: relative;
  z-index: 1;
}

.tools-title {
  margin-bottom: 1.2rem;
  color: var(--secondary-color);
  font-size: 2.4rem;
  font-weight: 700;
}

.tools-subtitle {
  margin-bottom: 3rem;
  color: var(--gray);
  font-size: 1.2rem;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

.tools-row {
  margin-top: 1.5rem;
}

@media (max-width: 991.98px) {
  .tools-section {
    padding: 60px 0;
  }
  
  .tools-title {
    font-size: 2.2rem;
  }
  
  .tools-subtitle {
    margin-bottom: 2.5rem;
  }
}

.tools-row {
  justify-content: center;
}

.tool-item {
  text-align: center;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 30px 20px;
  height: 100%;
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.tool-item:hover {
  transform: translateY(-10px);
  background-color: rgba(255, 255, 255, 0.15);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.tool-icon-container {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  transition: all 0.4s ease;
  position: relative;
  z-index: 1;
}

.tool-icon-container::before {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color), #3498db);
  opacity: 0;
  z-index: -1;
  transition: all 0.4s ease;
}

.tool-item:hover .tool-icon-container {
  background-color: rgba(255, 255, 255, 0.25);
  transform: scale(1.05);
}

.tool-item:hover .tool-icon-container::before {
  opacity: 1;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
}

.tool-icon {
  font-size: 2.2rem;
  color: #fff;
  transition: all 0.4s ease;
  display: block;
  margin: 0 auto;
  opacity: 1;
  text-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

.tool-item:hover .tool-icon {
  transform: scale(1.1);
  color: #fff;
}

.tool-name {
  font-size: 1.2rem;
  font-weight: 600;
  margin-top: 5px;
  margin-bottom: 10px;
  color: #fff;
  transition: all 0.3s ease;
}

.tool-description {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.95rem;
  margin-bottom: 15px;
  line-height: 1.5;
}

.tool-link {
  display: inline-block;
  color: #fff;
  font-weight: 500;
  font-size: 0.9rem;
  padding: 5px 0;
  position: relative;
  transition: all 0.3s ease;
}

.tool-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background-color: #fff;
  transition: all 0.3s ease;
}

.tool-link:hover {
  color: #fff;
}

.tool-link:hover::after {
  width: 100%;
}

/* 波纹效果 */
.ripple {
  position: absolute;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.3);
  transform: scale(0);
  animation: ripple 0.6s ease-out;
  pointer-events: none;
}

@keyframes ripple {
  to {
    transform: scale(2);
    opacity: 0;
  }
}

/* 工具区域动画 */
.fadeInDown {
  animation: fadeInDown 0.8s ease forwards;
}

.fadeInUp {
  animation: fadeInUp 0.8s ease forwards;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 767px) {
  .tool-icon-container {
    width: 80px;
    height: 80px;
  }
  
  .tool-icon {
    font-size: 2rem;
  }
  
  .tool-name {
    font-size: 0.85rem;
  }
}

@media (max-width: 576px) {
  .tool-icon-container {
    width: 70px;
    height: 70px;
  }
  
  .tool-icon {
    font-size: 1.8rem;
  }
  
  .tools-section {
    padding: 40px 0;
  }
  
  .tools-title {
    margin-bottom: 25px;
    font-size: 1.4rem;
  }
}

/* 轮播控制按钮 */
.carousel-control-prev,
.carousel-control-next {
  width: 5%;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
  width: 30px;
  height: 30px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  background-size: 50%;
}

#heroCarousel:hover .carousel-control-prev,
#heroCarousel:hover .carousel-control-next {
  opacity: 1;
}

/* 下拉菜单样式 */
.dropdown-menu {
  border: none;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
  padding: 0.5rem 0;
  margin-top: 0;
  border-radius: 4px;
}

.dropdown-item {
  padding: 0.5rem 1.5rem;
  color: var(--text-color);
  font-weight: 500;
  transition: all 0.2s ease;
}

.dropdown-item:hover,
.dropdown-item:focus {
  color: var(--primary-color);
  background-color: rgba(11, 118, 206, 0.05);
}

/* 二级下拉菜单样式 */
.dropdown-submenu {
  position: relative;
}

.dropdown-submenu > .dropdown-menu {
  top: 0;
  left: 100%;
  margin-top: -0.5rem;
  margin-left: 0;
  display: none;
}

.dropdown-submenu:hover > .dropdown-menu {
  display: block;
}

/* 下拉箭头样式 */
.dropdown-toggle::after {
  transition: transform 0.2s ease;
}

.dropdown-submenu > .dropdown-item::after {
  display: inline-block;
  margin-left: 0.5rem;
  vertical-align: middle;
  content: "";
  border-top: 0.3em solid transparent;
  border-right: 0;
  border-bottom: 0.3em solid transparent;
  border-left: 0.3em solid;
}

/* 移动端适配 */
@media (max-width: 991.98px) {
  .dropdown-menu {
    border: none;
    box-shadow: none;
    padding: 0;
    margin: 0;
  }

  .dropdown-submenu > .dropdown-menu {
    position: static;
    margin: 0;
    padding-left: 1rem;
    display: none;
  }

  .dropdown-item {
    padding: 0.5rem 1rem;
  }

  .dropdown-submenu > .dropdown-item::after {
    transform: rotate(90deg);
  }
}

/* 添加 Intersection Observer 相关样式 */
.initial-hidden {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.visible {
  opacity: 1;
  transform: translateY(0);
}

.footer h5 {
  color: #ffffff;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 1.1rem;
}

.footer ul li a {
  color: #B0B0B0 !important;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer ul li a:hover {
  color: #ffffff !important;
}

.footer ul li {
  color: #B0B0B0;
  margin-bottom: 0.5rem;
}

.footer .small {
  color: #B0B0B0;
}

.footer .small a {
  color: #B0B0B0;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer .small a:hover {
  color: #ffffff;
}

/* 联系页面样式 */
.contact-section {
  padding: 4rem 0;
  background-color: var(--light-bg);
}

.contact-card {
  background: var(--white);
  border-radius: 8px;
  padding: 2rem;
  height: 100%;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.contact-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.contact-icon {
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: 1.5rem;
}

.contact-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-color);
}

.contact-info {
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.map-container {
  height: 400px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.contact-form {
  background: var(--white);
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-control {
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 0.75rem;
  width: 100%;
  transition: border-color 0.3s ease;
}

.form-control:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.2);
}

.btn-submit {
  background-color: var(--primary-color);
  color: var(--white);
  padding: 0.75rem 2rem;
  border: none;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.btn-submit:hover {
  background-color: var(--primary-dark);
}

/* 联系页面响应式调整 */
@media (max-width: 768px) {
  .page-header {
    height: 30vh;
    min-height: 250px;
  }
  
  .page-header h1 {
    font-size: 2.25rem;
  }
  
  .page-header p {
    font-size: 1rem;
  }
  
  .contact-info-item {
    margin-bottom: 1.5rem;
  }
  
  .contact-icon {
    width: 40px;
    height: 40px;
    font-size: 1.25rem;
  }
  
  .map-container {
    height: 300px;
    margin-bottom: 2rem;
  }
  
  .office-card img {
    height: 150px;
  }
}

/* 联系表单增强样式 */
.contact-form label {
  color: var(--secondary-color);
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.contact-form .form-group {
  margin-bottom: 1.5rem;
}

.contact-form .form-control::placeholder {
  color: var(--gray);
  opacity: 0.8;
}

.contact-form .btn-submit {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.contact-form .btn-submit i {
  font-size: 1.1em;
}

/* 联系卡片增强样式 */
.contact-card {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.contact-card .contact-header {
  margin-bottom: 1.5rem;
}

.contact-card .contact-header h4 {
  color: var(--primary-color);
  margin-bottom: 0.75rem;
}

.contact-card .contact-content {
  flex-grow: 1;
}

.contact-card .contact-footer {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--gray-light);
}

/* 地图容器增强样式 */
.map-container iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.map-container .map-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.1);
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s;
}

.map-container:hover .map-overlay {
  opacity: 1;
}

/* 动画效果 */
.contact-info-item,
.contact-card,
.office-card {
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}