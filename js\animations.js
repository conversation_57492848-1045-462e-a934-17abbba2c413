// 页面加载时的动画效果
document.addEventListener('DOMContentLoaded', function() {
  // 导航栏动画
  const navbar = document.querySelector('.navbar');
  if (navbar) {
    navbar.classList.add('animate-fadeInDown');
  }

  // 页面标题动画
  const pageHeader = document.querySelector('.page-header');
  if (pageHeader) {
    pageHeader.classList.add('animate-fadeIn');
  }

  // 福利卡片动画
  const benefitCards = document.querySelectorAll('.benefit-card');
  benefitCards.forEach((card, index) => {
    setTimeout(() => {
      card.classList.add('animate-fadeInUp');
    }, index * 200);
  });

  // 职位卡片动画
  const jobCards = document.querySelectorAll('.job-card');
  jobCards.forEach((card, index) => {
    setTimeout(() => {
      card.classList.add('animate-fadeInUp');
    }, index * 200);
  });

  // 页脚动画
  const footer = document.querySelector('.footer');
  if (footer) {
    footer.classList.add('animate-fadeIn');
  }

  // 导航菜单项动画
  const navItems = document.querySelectorAll('.navbar-nav .nav-item');
  navItems.forEach((item, index) => {
    setTimeout(() => {
      item.classList.add('animate-fadeInLeft');
    }, index * 100);
  });

  // 初始化导航栏交互
  initNavigation();

  // 初始化滚动监听
  initScrollAnimation();

  // 初始化列表动画
  initListAnimation();

  // 初始化图片加载动画
  initImageLoadAnimation();

  // 初始化表单动画
  initFormAnimation();

  // 初始化所有动画效果
  initAllAnimations();
});

// 初始化列表动画
function initListAnimation() {
  const lists = document.querySelectorAll('.animate-list');
  
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('visible');
      }
    });
  }, {
    threshold: 0.1,
    rootMargin: '0px 0px -10% 0px'
  });

  lists.forEach(list => observer.observe(list));
}

// 初始化图片加载动画
function initImageLoadAnimation() {
  const images = document.querySelectorAll('.hover-zoom img');
  
  images.forEach(img => {
    // 添加加载动画
    const spinner = document.createElement('div');
    spinner.className = 'loading-spinner';
    img.parentNode.insertBefore(spinner, img);
    
    // 图片加载完成后移除加载动画
    img.addEventListener('load', () => {
      spinner.remove();
      img.style.opacity = '1';
    });
    
    // 图片加载失败处理
    img.addEventListener('error', () => {
      spinner.remove();
      img.style.opacity = '1';
      // 可以在这里添加失败处理逻辑
    });
  });
}

// 初始化表单动画
function initFormAnimation() {
  const forms = document.querySelectorAll('form');
  
  forms.forEach(form => {
    // 表单提交动画
    form.addEventListener('submit', function(e) {
      const submitBtn = form.querySelector('[type="submit"]');
      if (submitBtn) {
        const originalText = submitBtn.innerHTML;
        const spinner = document.createElement('div');
        spinner.className = 'loading-spinner';
        submitBtn.innerHTML = '';
        submitBtn.appendChild(spinner);
        
        // 模拟表单提交后恢复按钮状态
        setTimeout(() => {
          submitBtn.innerHTML = originalText;
        }, 2000);
      }
    });
    
    // 输入框动画
    const inputs = form.querySelectorAll('.form-control');
    inputs.forEach(input => {
      input.addEventListener('focus', function() {
        this.parentNode.classList.add('focused');
      });
      
      input.addEventListener('blur', function() {
        if (!this.value) {
          this.parentNode.classList.remove('focused');
        }
      });
    });
  });
}

// 滚动监听初始化
function initScrollAnimation() {
  const sections = document.querySelectorAll('.section-block');
  const contentBlocks = document.querySelectorAll('.content-block');
  
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('visible');
        
        // 处理子元素的动画
        const animatedChildren = entry.target.querySelectorAll('[class*="animate-"]');
        animatedChildren.forEach((child, index) => {
          setTimeout(() => {
            child.classList.add('visible');
          }, index * 100);
        });
      }
    });
  }, {
    threshold: 0.1,
    rootMargin: '0px 0px -10% 0px'
  });

  sections.forEach(section => observer.observe(section));
  contentBlocks.forEach(block => observer.observe(block));
}

// 导航栏滚动效果
window.addEventListener('scroll', function() {
  const mainNav = document.querySelector('#mainNav');
  if (!mainNav) return;
  
  if (window.scrollY > 50) {
    mainNav.classList.add('navbar-scrolled');
  } else {
    mainNav.classList.remove('navbar-scrolled');
  }
});

// 窗口大小改变时重新初始化导航栏
window.addEventListener('resize', function() {
  initNavigation();
});

// 按钮悬停效果
document.addEventListener('DOMContentLoaded', function() {
  const buttons = document.querySelectorAll('.btn');
  buttons.forEach(button => {
    button.addEventListener('mouseenter', function() {
      this.style.transform = 'translateY(-2px)';
      this.style.boxShadow = '0 4px 8px rgba(0,0,0,0.2)';
    });
    
    button.addEventListener('mouseleave', function() {
      this.style.transform = 'translateY(0)';
      this.style.boxShadow = 'none';
    });
  });
});

// 初始化所有动画效果
function initAllAnimations() {
    // 初始化导航栏动画
    initNavbarAnimations();
    
    // 初始化滚动动画
    initScrollAnimations();
    
    // 初始化视差滚动
    initParallax();
    
    // 初始化3D卡片效果
    init3DCards();
    
    // 初始化滚动进度条
    initScrollProgress();
    
    // 初始化打字机效果
    initTypingEffect();
    
    // 初始化按钮波纹效果
    initRippleEffect();
    
    // 初始化图标动画
    initIconAnimations();
    
    // 初始化滚动提示
    initScrollIndicator();
}

// 导航栏动画
function initNavbarAnimations() {
    const navItems = document.querySelectorAll('.navbar-nav .nav-item');
    navItems.forEach((item, index) => {
        item.style.animation = `slideInLeft 0.5s ease forwards ${index * 0.1}s`;
    });
}

// 滚动动画
function initScrollAnimations() {
    const animatedElements = document.querySelectorAll('[class*="animate-"]');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.visibility = 'visible';
                entry.target.style.animation = `${getAnimationName(entry.target)} 1s ease forwards`;
                observer.unobserve(entry.target);
            }
        });
    }, { threshold: 0.1 });
    
    animatedElements.forEach(element => {
        element.style.visibility = 'hidden';
        observer.observe(element);
    });
}

// 获取动画名称
function getAnimationName(element) {
    const classes = element.className.split(' ');
    for (const className of classes) {
        if (className.startsWith('animate-')) {
            return className.replace('animate-', '');
        }
    }
    return 'fadeIn';
}

// 视差滚动效果
function initParallax() {
    const parallaxElements = document.querySelectorAll('.parallax-container');
    
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        
        parallaxElements.forEach(element => {
            const speed = element.dataset.speed || 0.5;
            element.style.transform = `translateY(${scrolled * speed}px)`;
        });
    });
}

// 3D卡片效果
function init3DCards() {
    const cards = document.querySelectorAll('.card-3d');
    
    cards.forEach(card => {
        card.addEventListener('mousemove', handleCardMove);
        card.addEventListener('mouseleave', handleCardLeave);
    });
}

function handleCardMove(e) {
    const card = e.currentTarget;
    const rect = card.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    const centerX = rect.width / 2;
    const centerY = rect.height / 2;
    
    const rotateX = (y - centerY) / 10;
    const rotateY = (centerX - x) / 10;
    
    card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
}

function handleCardLeave(e) {
    const card = e.currentTarget;
    card.style.transform = 'perspective(1000px) rotateX(0) rotateY(0)';
}

// 滚动进度条
function initScrollProgress() {
    const progressBar = document.createElement('div');
    progressBar.className = 'scroll-progress';
    document.body.appendChild(progressBar);
    
    window.addEventListener('scroll', () => {
        const scrolled = (window.pageYOffset / (document.documentElement.scrollHeight - window.innerHeight)) * 100;
        progressBar.style.width = `${scrolled}%`;
    });
}

// 打字机效果
function initTypingEffect() {
    const typingElements = document.querySelectorAll('.typing-text');
    
    typingElements.forEach(element => {
        const text = element.textContent;
        element.textContent = '';
        let index = 0;
        
        function type() {
            if (index < text.length) {
                element.textContent += text.charAt(index);
                index++;
                setTimeout(type, 100);
            }
        }
        
        const observer = new IntersectionObserver((entries) => {
            if (entries[0].isIntersecting) {
                type();
                observer.unobserve(element);
            }
        });
        
        observer.observe(element);
    });
}

// 按钮波纹效果
function initRippleEffect() {
    const buttons = document.querySelectorAll('.btn-ripple');
    
    buttons.forEach(button => {
        button.addEventListener('click', createRipple);
    });
}

function createRipple(e) {
    const button = e.currentTarget;
    const ripple = document.createElement('span');
    const rect = button.getBoundingClientRect();
    
    const size = Math.max(rect.width, rect.height);
    const x = e.clientX - rect.left - size / 2;
    const y = e.clientY - rect.top - size / 2;
    
    ripple.style.width = ripple.style.height = `${size}px`;
    ripple.style.left = `${x}px`;
    ripple.style.top = `${y}px`;
    ripple.className = 'ripple';
    
    button.appendChild(ripple);
    setTimeout(() => ripple.remove(), 600);
}

// 图标动画
function initIconAnimations() {
    const animatedIcons = document.querySelectorAll('.icon-animated');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('icon-animate');
            }
        });
    });
    
    animatedIcons.forEach(icon => observer.observe(icon));
}

// 滚动提示
function initScrollIndicator() {
    const sections = document.querySelectorAll('section');
    
    sections.forEach(section => {
        if (section.offsetHeight > window.innerHeight) {
            const indicator = document.createElement('div');
            indicator.className = 'scroll-indicator';
            indicator.innerHTML = '<i class="fas fa-chevron-down"></i>';
            section.appendChild(indicator);
            
            // 当滚动到底部时隐藏指示器
            window.addEventListener('scroll', () => {
                const rect = section.getBoundingClientRect();
                if (rect.bottom <= window.innerHeight) {
                    indicator.style.opacity = '0';
                } else {
                    indicator.style.opacity = '1';
                }
            });
        }
    });
} 