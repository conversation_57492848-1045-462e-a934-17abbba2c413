/*
 * synology-animations.css - Lunfly 动画与过渡效果
 * 模仿 Synology 官网的动画和交互效果
 */

/* 内容过渡动画 */
.animate-in {
  animation: fadeInUp 0.6s ease forwards;
}

/* 淡入上移动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 鼠标悬浮效果 */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
}

/* 按钮悬浮效果 */
.btn {
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.1);
  opacity: 0;
  transform: scale(0.5);
  transition: opacity 0.3s ease, transform 0.3s ease;
  z-index: -1;
}

.btn:hover::after {
  opacity: 1;
  transform: scale(1);
}

/* 图片悬浮效果 */
.solution-img-container {
  overflow: hidden;
}

.solution-img-container img {
  transition: transform 0.5s ease;
}

.solution-item:hover .solution-img-container img {
  transform: scale(1.05);
}

/* 导航栏滚动效果 */
#mainNav {
  transition: background-color 0.3s ease, padding 0.3s ease, box-shadow 0.3s ease;
}

/* 卡片点击效果 */
.card, .support-item, .timeline-content {
  position: relative;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:active, .support-item:active, .timeline-content:active {
  transform: scale(0.98);
}

/* 链接悬浮效果 */
.footer-link {
  position: relative;
}

.footer-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 1px;
  background-color: currentColor;
  transition: width 0.3s ease;
}

.footer-link:hover::after {
  width: 100%;
}

/* 图标脉冲效果 */
.icon-pulse {
  animation: iconPulse 2s infinite;
}

@keyframes iconPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* 页面加载效果 */
body {
  opacity: 0;
  transition: opacity 0.5s ease;
}

body.loaded {
  opacity: 1;
}

/* 滚动指示器 */
.scroll-indicator {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  animation: scrollBounce 2s infinite;
}

@keyframes scrollBounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0) translateX(-50%);
  }
  40% {
    transform: translateY(-10px) translateX(-50%);
  }
  60% {
    transform: translateY(-5px) translateX(-50%);
  }
}

/* 高亮文本 */
.highlight {
  color: var(--main-color);
  position: relative;
  display: inline-block;
}

.highlight::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 6px;
  background-color: rgba(11, 118, 206, 0.2);
  z-index: -1;
}

/* 数字增长动画 */
.count-up {
  display: inline-block;
  transition: transform 0.3s ease;
}

.count-up.active {
  transform: scale(1.2);
} 