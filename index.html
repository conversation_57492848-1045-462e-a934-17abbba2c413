<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Lunfly - 工业智能解决方案</title>
  <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
  <link href="css/main.css" rel="stylesheet">
  <link href="css/extracted_navbar.css" rel="stylesheet">
  <link href="css/tool-item.css" rel="stylesheet">
  <link href="css/animations.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
</head>
<body>
  <!-- 顶部导航栏 -->
  <nav class="navbar navbar-expand-lg fixed-top" id="mainNav">
    <div class="container">
      <a class="navbar-brand initial-hidden animate-slideInLeft" href="index.html"><img src="images/logo.png" alt="Lunfly Logo" class="navbar-logo">Lunfly</a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarResponsive" aria-controls="navbarResponsive" aria-expanded="false" aria-label="Toggle navigation">
        <i class="bi bi-list"></i>
      </button>
      <div class="collapse navbar-collapse" id="navbarResponsive">
        <ul class="navbar-nav ms-auto">
          <!-- 产品与服务下拉菜单 -->
          <li class="nav-item dropdown initial-hidden animate-slideInLeft animate-delay-1">
            <a class="nav-link dropdown-toggle" href="#products" id="productsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
              产品与服务
            </a>
            <ul class="dropdown-menu" aria-labelledby="productsDropdown">
              <li class="dropdown-submenu">
                <a class="dropdown-item dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">控制系统</a>
                <ul class="dropdown-menu">
                  <li><a class="dropdown-item" href="webs_series.html">WEBs 系列</a></li>
                  <li><a class="dropdown-item" href="trend_series.html">Trend 系列</a></li>
                  <li><a class="dropdown-item" href="hotel_room_control.html">酒店客房控制</a></li>
                </ul>
              </li>
              <li class="dropdown-submenu">
                <a class="dropdown-item dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">智能设备</a>
                <ul class="dropdown-menu">
                  <li><a class="dropdown-item" href="valve_actuator.html">阀门与执行器</a></li>
                  <li><a class="dropdown-item" href="sensor.html">传感器</a></li>
                  <li><a class="dropdown-item" href="thermostat.html">温控器</a></li>
                </ul>
              </li>
              <li class="dropdown-submenu">
                <a class="dropdown-item dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">技术服务</a>
                <ul class="dropdown-menu">
                  <li><a class="dropdown-item" href="system_integration.html">系统集成</a></li>
                  <li><a class="dropdown-item" href="technical_consulting.html">技术咨询</a></li>
                  <li><a class="dropdown-item" href="operation_maintenance.html">运维服务</a></li>
                </ul>
              </li>
            </ul>
          </li>
          
          <!-- 解决方案下拉菜单 -->
          <li class="nav-item dropdown initial-hidden animate-slideInLeft animate-delay-2">
            <a class="nav-link dropdown-toggle" href="#solutions" id="solutionsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
              解决方案
            </a>
            <ul class="dropdown-menu" aria-labelledby="solutionsDropdown">
              <li class="dropdown-submenu">
                <a class="dropdown-item dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">楼宇自控</a>
                <ul class="dropdown-menu">
                  <li><a class="dropdown-item" href="commercial.html">商业楼宇</a></li>
                  <li><a class="dropdown-item" href="hospital.html">医院</a></li>
                  <li><a class="dropdown-item" href="transportation.html">轨道交通</a></li>
                </ul>
              </li>
              <li class="dropdown-submenu">
                <a class="dropdown-item dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">节能改造</a>
                <ul class="dropdown-menu">
                  <li><a class="dropdown-item" href="heat-recovery.html">余热回收</a></li>
                  <li><a class="dropdown-item" href="alternative-energy.html">替代能源</a></li>
                </ul>
              </li>
              <li class="dropdown-submenu">
                <a class="dropdown-item dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">智慧农业</a>
                <ul class="dropdown-menu">
                  <li><a class="dropdown-item" href="environment-control.html">环境调控</a></li>
                  <li><a class="dropdown-item" href="data-collection.html">数据采集</a></li>
                </ul>
              </li>
            </ul>
          </li>
          
          <!-- 技术支持下拉菜单 -->
          <li class="nav-item dropdown initial-hidden animate-slideInLeft animate-delay-3">
            <a class="nav-link dropdown-toggle" href="#support" id="supportDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
              技术支持
            </a>
            <ul class="dropdown-menu" aria-labelledby="supportDropdown">
              <li><a class="dropdown-item" href="online-service.html">在线报修</a></li>
              <li><a class="dropdown-item" href="remote-diagnosis.html">远程诊断</a></li>
            </ul>
          </li>
          
          <!-- 关于我们下拉菜单 -->
          <li class="nav-item dropdown initial-hidden animate-slideInLeft animate-delay-4">
            <a class="nav-link dropdown-toggle" href="#about" id="aboutDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
              关于我们
            </a>
            <ul class="dropdown-menu" aria-labelledby="aboutDropdown">
              <li><a class="dropdown-item" href="history.html">发展历程</a></li>
              <li><a class="dropdown-item" href="contact.html">联系方式</a></li>
              <li><a class="dropdown-item" href="recruitment.html">伦飞招聘</a></li>
            </ul>
          </li>
        </ul>
      </div>
    </div>
  </nav>

  <!-- Hero Banner -->
  <header class="hero" id="page-top">
    <div id="heroCarousel" class="carousel slide" data-bs-ride="carousel">
      <div class="carousel-inner">
        <!-- Slide 1: 智能楼宇控制系统 -->
        <div class="carousel-item active" style="background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('images/hero/hero-building.jpg') no-repeat center center; background-size: cover;">
          <div class="container">
            <div class="row align-items-center">
              <div class="col-lg-8 col-md-10 mx-auto hero-content">
                <h1 class="hero-title initial-hidden animate-slideInLeft">智能楼宇控制系统</h1>
                <p class="hero-subtitle initial-hidden animate-slideInLeft animate-delay-1">打造高效、节能、智能的建筑管理平台</p>
                <a class="btn hero-btn initial-hidden animate-slideInLeft animate-delay-2" href="#solutions">探索解决方案</a>
              </div>
            </div>
          </div>
        </div>

        <!-- Slide 2: 能源优化方案 -->
        <div class="carousel-item" style="background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('images/hero/hero-energy.jpg') no-repeat center center; background-size: cover;">
          <div class="container">
            <div class="row align-items-center">
              <div class="col-lg-8 col-md-10 mx-auto hero-content">
                <h1 class="hero-title initial-hidden animate-slideInLeft">能源优化方案</h1>
                <p class="hero-subtitle initial-hidden animate-slideInLeft animate-delay-1">实现建筑能源效率最大化，降低运营成本</p>
                <a class="btn hero-btn initial-hidden animate-slideInLeft animate-delay-2" href="#solutions">查看详情</a>
              </div>
            </div>
          </div>
        </div>

        <!-- Slide 3: 智慧农业解决方案 -->
        <div class="carousel-item" style="background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('images/hero/hero-agriculture.jpg') no-repeat center center; background-size: cover;">
          <div class="container">
            <div class="row align-items-center">
              <div class="col-lg-8 col-md-10 mx-auto hero-content">
                <h1 class="hero-title initial-hidden animate-slideInLeft">智慧农业解决方案</h1>
                <p class="hero-subtitle initial-hidden animate-slideInLeft animate-delay-1">推动农业现代化与智能化发展，提升生产效率</p>
                <a class="btn hero-btn initial-hidden animate-slideInLeft animate-delay-2" href="#solutions">了解更多</a>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 轮播指示器与控制 -->
      <div class="carousel-indicators">
        <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="0" class="active" aria-current="true" aria-label="智能楼宇"></button>
        <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="1" aria-label="能源优化"></button>
        <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="2" aria-label="智慧农业"></button>
      </div>

      <button class="carousel-control-prev" type="button" data-bs-target="#heroCarousel" data-bs-slide="prev">
        <span class="carousel-control-prev-icon" aria-hidden="true"></span>
        <span class="visually-hidden">上一个</span>
      </button>
      <button class="carousel-control-next" type="button" data-bs-target="#heroCarousel" data-bs-slide="next">
        <span class="carousel-control-next-icon" aria-hidden="true"></span>
        <span class="visually-hidden">下一个</span>
      </button>
    </div>
  </header>

  <!-- 功能区块 -->
  <section class="section feature-blocks">
    <div class="container-fluid p-0">
      <div class="row g-4">
        <div class="col-md-3">
          <div class="feature-block initial-hidden animate-slideInLeft" style="background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('images/features/feature-control.jpg') no-repeat center center; background-size: cover;">
            <div class="feature-content">
              <h3>智能控制系统</h3>
              <p class="feature-desc">集成化的建筑设备管理与控制系统，实现智能化运营</p>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="feature-block initial-hidden animate-slideInLeft animate-delay-1" style="background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('images/features/feature-energy.jpg') no-repeat center center; background-size: cover;">
            <div class="feature-content">
              <h3>能源管理平台</h3>
              <p class="feature-desc">实时监控与分析能源使用情况，优化能源效率</p>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="feature-block initial-hidden animate-slideInLeft animate-delay-2" style="background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('images/features/feature-agriculture.jpg') no-repeat center center; background-size: cover;">
            <div class="feature-content">
              <h3>农业智能监测</h3>
              <p class="feature-desc">精准的环境监测与控制系统，提升农业生产效率</p>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="feature-block initial-hidden animate-slideInLeft animate-delay-3" style="background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('images/features/feature-data.jpg') no-repeat center center; background-size: cover;">
            <div class="feature-content">
              <h3>数据分析服务</h3>
              <p class="feature-desc">基于大数据的智能分析与决策支持系统</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- 工具区域 -->
  <section class="section tools-section section-block">
    <div class="container text-center">
      <h2 class="tools-title content-block">我们可以为您做些什么？</h2>
      <p class="tools-subtitle content-block delay-1">探索我们的专业工具与服务，助力您的智能化项目</p>
      <div class="row tools-row g-4">
        <div class="col-md-4 col-sm-6 col-12 mb-4 content-block">
          <a href="building_control.html" class="tool-item-link">
            <div class="tool-item initial-hidden animate-slideInLeft">
              <div class="tool-icon-container">
                <i class="bi bi-building-gear tool-icon"></i>
              </div>
              <h4 class="tool-name">楼宇控制方案</h4>
              <p class="tool-description">定制化的智能楼宇控制系统解决方案</p>
            </div>
          </a>
        </div>
        <div class="col-md-4 col-sm-6 col-12 mb-4 content-block delay-1">
          <a href="energy_management.html" class="tool-item-link">
            <div class="tool-item initial-hidden animate-slideInLeft animate-delay-1">
              <div class="tool-icon-container">
                <i class="bi bi-lightning-charge tool-icon"></i>
              </div>
              <h4 class="tool-name">能源管理工具</h4>
              <p class="tool-description">能源消耗分析与优化建议</p>
            </div>
          </a>
        </div>
        <div class="col-md-4 col-sm-6 col-12 mb-4 content-block delay-2">
          <a href="environment_monitoring.html" class="tool-item-link">
            <div class="tool-item initial-hidden animate-slideInLeft animate-delay-2">
              <div class="tool-icon-container">
                <i class="bi bi-thermometer-half tool-icon"></i>
              </div>
              <h4 class="tool-name">环境监控系统</h4>
              <p class="tool-description">实时监测与控制室内环境参数</p>
            </div>
          </a>
        </div>
        <div class="col-md-4 col-sm-6 col-12 mb-4 content-block delay-1">
          <a href="energy_calculator.html" class="tool-item-link">
            <div class="tool-item initial-hidden animate-slideInLeft animate-delay-1">
              <div class="tool-icon-container">
                <i class="bi bi-calculator tool-icon"></i>
              </div>
              <h4 class="tool-name">节能效益计算器</h4>
              <p class="tool-description">评估节能改造项目的投资回报</p>
            </div>
          </a>
        </div>
        <div class="col-md-4 col-sm-6 col-12 mb-4 content-block delay-2">
          <a href="maintenance_service.html" class="tool-item-link">
            <div class="tool-item initial-hidden animate-slideInLeft animate-delay-2">
              <div class="tool-icon-container">
                <i class="bi bi-tools tool-icon"></i>
              </div>
              <h4 class="tool-name">系统维护服务</h4>
              <p class="tool-description">专业的设备维护与故障诊断</p>
            </div>
          </a>
        </div>
        <div class="col-md-4 col-sm-6 col-12 mb-4 content-block delay-3">
          <a href="tech_resources.html" class="tool-item-link">
            <div class="tool-item initial-hidden animate-slideInLeft animate-delay-3">
              <div class="tool-icon-container">
                <i class="bi bi-book tool-icon"></i>
              </div>
              <h4 class="tool-name">技术资源库</h4>
              <p class="tool-description">产品手册、技术文档与培训资料</p>
            </div>
          </a>
        </div>
      </div>
    </div>
  </section>

  <!-- 页脚 -->
  <footer class="footer bg-dark py-5">
    <div class="container">
      <div class="row">
        <div class="col-lg-3 mb-4">
          <h5 class="mb-4">关于我们</h5>
          <ul class="list-unstyled">
            <li><a href="company_profile.html">公司简介</a></li>
            <li><a href="history.html">发展历程</a></li>
            <li><a href="team.html">团队介绍</a></li>
            <li><a href="culture.html">企业文化</a></li>
          </ul>
        </div>
        <div class="col-lg-3 mb-4">
          <h5 class="mb-4">产品与服务</h5>
          <ul class="list-unstyled">
            <li><a href="intelligent_control.html">智能控制系统</a></li>
            <li><a href="energy_management.html">能源管理平台</a></li>
            <li><a href="agriculture_monitoring.html">农业智能监测</a></li>
            <li><a href="data_analysis.html">数据采集监控</a></li>
          </ul>
        </div>
        <div class="col-lg-3 mb-4">
          <h5 class="mb-4">解决方案</h5>
          <ul class="list-unstyled">
            <li><a href="building_automation.html">楼宇自动化</a></li>
            <li><a href="energy_optimization.html">能源优化</a></li>
            <li><a href="smart_agriculture.html">智慧农业</a></li>
            <li><a href="system_integration.html">系统集成</a></li>
          </ul>
        </div>
        <div class="col-lg-3">
          <h5 class="mb-4">联系我们</h5>
          <ul class="list-unstyled">
            <li><i class="bi bi-geo-alt me-2"></i>地址: 北京市朝阳区建国路88号</li>
            <li><i class="bi bi-telephone me-2"></i>电话: 010-12345678</li>
            <li><i class="bi bi-envelope me-2"></i>邮箱: <EMAIL></li>
          </ul>
        </div>
      </div>
      <hr class="my-4">
      <div class="row">
        <div class="col-12 text-center small">
          版权所有 ©2025 北京伦飞天成科贸有限公司 | 京公网安备 123456789123456号 | 京ICP备12345678号 | <a href="#">条款条约</a> | <a href="#">隐私声明</a> | Honeywell授权代理商
        </div>
      </div>
    </div>
  </footer>
  
  <!-- Scripts -->
  <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
  <script src="js/main.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // 观察section块
      const sectionObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('visible');
            
            // 当section可见时，触发其中的content blocks动画
            const contentBlocks = entry.target.querySelectorAll('.content-block');
            contentBlocks.forEach(block => {
              block.classList.add('visible');
            });
            
            sectionObserver.unobserve(entry.target);
          }
        });
      }, {
        threshold: 0.1,
        rootMargin: '0px 0px -100px 0px'
      });

      // 观察所有section块
      document.querySelectorAll('.section-block').forEach(section => {
        sectionObserver.observe(section);
      });

      // 轮播图切换时的动画
      const carousel = document.getElementById('heroCarousel');
      carousel.addEventListener('slide.bs.carousel', function () {
        const activeSlide = this.querySelector('.carousel-item.active');
        const elements = activeSlide.querySelectorAll('.initial-hidden');
        elements.forEach(element => {
          element.classList.add('animate-fadeInUp');
          element.classList.remove('initial-hidden');
        });
      });
    });
  </script>
</body>
</html>