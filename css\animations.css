/* Color Scheme */
:root {
  --primary-color: #002E5D;
  --secondary-color: #E31837;
  --white: #ffffff;
  --gray-100: #f8f9fa;
  --gray-200: #e9ecef;
  --gray-300: #dee2e6;
  --gray-400: #ced4da;
  --gray-500: #adb5bd;
  --gray-600: #6c757d;
  --gray-700: #495057;
  --gray-800: #343a40;
  --gray-900: #212529;
  --animation-duration: 0.6s;
  --animation-timing: cubic-bezier(0.4, 0, 0.2, 1);
  --animation-delay-base: 0.1s;
}

/* Enhanced Entrance Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes rotateIn {
  from {
    opacity: 0;
    transform: rotate(-10deg) scale(0.95);
  }
  to {
    opacity: 1;
    transform: rotate(0) scale(1);
  }
}

/* Animation Classes */
.animate-fadeIn {
  animation: fadeIn var(--animation-duration) var(--animation-timing) forwards;
}

.animate-fadeInUp {
  animation: fadeInUp var(--animation-duration) var(--animation-timing) forwards;
}

.animate-fadeInDown {
  animation: fadeInDown var(--animation-duration) var(--animation-timing) forwards;
}

.animate-fadeInLeft {
  animation: fadeInLeft var(--animation-duration) var(--animation-timing) forwards;
}

.animate-fadeInRight {
  animation: fadeInRight var(--animation-duration) var(--animation-timing) forwards;
}

.animate-scaleIn {
  animation: scaleIn var(--animation-duration) var(--animation-timing) forwards;
}

.animate-rotateIn {
  animation: rotateIn var(--animation-duration) var(--animation-timing) forwards;
}

/* Animation Delays */
.animate-delay-1 {
  animation-delay: calc(var(--animation-delay-base) * 1);
}

.animate-delay-2 {
  animation-delay: calc(var(--animation-delay-base) * 2);
}

.animate-delay-3 {
  animation-delay: calc(var(--animation-delay-base) * 3);
}

.animate-delay-4 {
  animation-delay: calc(var(--animation-delay-base) * 4);
}

.animate-delay-5 {
  animation-delay: calc(var(--animation-delay-base) * 5);
}

/* Initial State */
.initial-hidden {
  opacity: 0;
}

/* Section Styles */
.section-block {
  padding: 5rem 0;
  opacity: 0;
  transform: translateY(20px);
  transition: all var(--animation-duration) var(--animation-timing);
}

.section-block.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Content Block Styles */
.content-block {
  opacity: 0;
  transform: translateY(20px);
  transition: all var(--animation-duration) var(--animation-timing);
}

.content-block.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Navigation Styles */
.navbar {
  transition: all 0.3s ease;
}

.navbar-brand {
  transition: all 0.3s ease;
}

.nav-link {
  transition: all 0.3s ease;
}

/* Button Styles */
.btn {
  transition: all 0.3s ease;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* Hero Section Animations */
.hero-title {
  animation: fadeInUp 1s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

.hero-subtitle {
  animation: fadeInUp 1s cubic-bezier(0.16, 1, 0.3, 1) 0.2s forwards;
  opacity: 0;
}

.hero-btn {
  animation: fadeInUp 1s cubic-bezier(0.16, 1, 0.3, 1) 0.4s forwards;
  opacity: 0;
}

.hero-image {
  animation: slideInRight 1s cubic-bezier(0.16, 1, 0.3, 1) 0.3s forwards;
  opacity: 0;
}

/* Feature Blocks Animation */
.feature-block {
  transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  transform-origin: center;
}

.feature-block:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* Tool Items Animation */
.tool-item {
  transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  transform-origin: center;
}

.tool-item:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* Card Styles */
.tool-item {
  background-color: var(--white);
  border: 1px solid var(--gray-300);
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Text Colors */
h1, h2, h3, h4, h5, h6 {
  color: var(--gray-900);
  transition: color 0.3s ease;
}

p {
  color: var(--gray-600);
  transition: color 0.3s ease;
}

/* Section Backgrounds */
.section {
  background-color: var(--section-bg);
  padding: 80px 0;
  transition: background-color 0.3s ease;
}

/* Navbar Styles */
.navbar {
  background-color: var(--background-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.navbar-brand {
  color: var(--primary-color);
  font-weight: bold;
  transition: color 0.3s ease;
}

.nav-link {
  color: var(--text-color);
  transition: all 0.3s ease;
  position: relative;
}

.nav-link:hover {
  color: var(--primary-color);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--primary-color);
  transition: width 0.3s ease;
}

.nav-link:hover::after {
  width: 100%;
}

/* Section Block Animations */
.section-block {
  opacity: 0;
  transform: translateX(-80px);
  transition: all 1.2s cubic-bezier(0.22, 1, 0.36, 1);
  will-change: transform, opacity;
}

.section-block.visible {
  opacity: 1;
  transform: translateX(0);
}

/* Content Block Animations */
.content-block {
  opacity: 0;
  transform: translateY(40px);
  transition: all 1.2s cubic-bezier(0.22, 1, 0.36, 1);
  will-change: transform, opacity;
}

.content-block.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Staggered Delays for Content Blocks */
.delay-1 {
  transition-delay: 0.3s;
}

.delay-2 {
  transition-delay: 0.6s;
}

.delay-3 {
  transition-delay: 0.9s;
}

/* Special Element Animations */
/* Card Styles */
.card {
  transition: all 0.3s var(--animation-timing);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* Feature Icon Styles */
.feature-icon {
  transition: all 0.3s var(--animation-timing);
}

.feature-icon:hover {
  transform: scale(1.1);
  color: var(--primary-color);
}

/* Button Styles */
.btn {
  transition: all 0.3s var(--animation-timing);
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Image Styles */
.hover-zoom {
  overflow: hidden;
}

.hover-zoom img {
  transition: all 0.3s var(--animation-timing);
}

.hover-zoom:hover img {
  transform: scale(1.05);
}

/* Link Styles */
.hover-underline {
  position: relative;
  text-decoration: none;
}

.hover-underline::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -2px;
  left: 0;
  background-color: var(--primary-color);
  transition: width 0.3s var(--animation-timing);
}

.hover-underline:hover::after {
  width: 100%;
}

/* List Styles */
.animate-list li {
  opacity: 0;
  transform: translateX(-20px);
  transition: all 0.3s var(--animation-timing);
}

.animate-list.visible li {
  opacity: 1;
  transform: translateX(0);
}

.animate-list li:nth-child(1) { transition-delay: calc(var(--animation-delay-base) * 1); }
.animate-list li:nth-child(2) { transition-delay: calc(var(--animation-delay-base) * 2); }
.animate-list li:nth-child(3) { transition-delay: calc(var(--animation-delay-base) * 3); }
.animate-list li:nth-child(4) { transition-delay: calc(var(--animation-delay-base) * 4); }
.animate-list li:nth-child(5) { transition-delay: calc(var(--animation-delay-base) * 5); }

/* Form Styles */
.form-control {
  transition: all 0.3s var(--animation-timing);
}

.form-control:focus {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Loading Animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

/* Progress Bar Styles */
@keyframes progress {
  from {
    width: 0;
  }
  to {
    width: var(--progress-width, 100%);
  }
}

.progress-bar {
  animation: progress 1s var(--animation-timing) forwards;
}

/* 视差滚动效果 */
.parallax-container {
  position: relative;
  overflow: hidden;
  height: 100vh;
  perspective: 1000px;
}

.parallax-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  transform-style: preserve-3d;
  will-change: transform;
}

.parallax-bg {
  transform: translateZ(-100px) scale(1.15);
}

.parallax-content {
  transform: translateZ(0);
}

.parallax-front {
  transform: translateZ(100px) scale(0.85);
}

/* 3D 卡片效果 */
.card-3d {
  perspective: 1000px;
  transform-style: preserve-3d;
}

.card-3d-inner {
  position: relative;
  width: 100%;
  height: 100%;
  transition: transform 0.6s var(--animation-timing);
  transform-style: preserve-3d;
}

.card-3d:hover .card-3d-inner {
  transform: rotateY(10deg) rotateX(5deg);
}

/* 波浪动画 */
@keyframes wave {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0);
  }
}

.wave-animation {
  animation: wave 2s var(--animation-timing) infinite;
}

/* 渐变背景动画 */
@keyframes gradientBG {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.gradient-bg {
  background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
  background-size: 200% 200%;
  animation: gradientBG 10s ease infinite;
}

/* 打字机效果 */
@keyframes typing {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

.typing-text {
  overflow: hidden;
  white-space: nowrap;
  border-right: 2px solid var(--primary-color);
  animation: typing 3s steps(40, end),
             blink-caret 0.75s step-end infinite;
}

@keyframes blink-caret {
  from, to {
    border-color: transparent;
  }
  50% {
    border-color: var(--primary-color);
  }
}

/* 图片悬停效果增强 */
.image-hover {
  position: relative;
  overflow: hidden;
}

.image-hover img {
  transition: all 0.5s var(--animation-timing);
}

.image-hover::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  opacity: 0;
  transition: all 0.5s var(--animation-timing);
}

.image-hover:hover img {
  transform: scale(1.1);
}

.image-hover:hover::before {
  opacity: 1;
}

/* 按钮点击波纹效果 */
.btn-ripple {
  position: relative;
  overflow: hidden;
}

.btn-ripple::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s ease-out, height 0.6s ease-out;
}

.btn-ripple:active::after {
  width: 200%;
  height: 200%;
}

/* 滚动进度条 */
.scroll-progress {
  position: fixed;
  top: 0;
  left: 0;
  width: 0;
  height: 3px;
  background: var(--primary-color);
  z-index: 10000;
  transition: width 0.1s linear;
}

/* 页面切换动画 */
.page-transition {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--primary-color);
  transform: translateY(100%);
  transition: transform 0.6s var(--animation-timing);
  z-index: 9999;
}

.page-transition.active {
  transform: translateY(0);
}

/* 卡片堆叠效果 */
.card-stack {
  position: relative;
}

.card-stack-item {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  transition: all 0.3s var(--animation-timing);
}

.card-stack-item:nth-child(1) { transform: translateY(0); }
.card-stack-item:nth-child(2) { transform: translateY(10px); }
.card-stack-item:nth-child(3) { transform: translateY(20px); }

.card-stack:hover .card-stack-item:nth-child(1) { transform: translateY(-10px); }
.card-stack:hover .card-stack-item:nth-child(2) { transform: translateY(0); }
.card-stack:hover .card-stack-item:nth-child(3) { transform: translateY(10px); }

/* 图标动画增强 */
.icon-pulse {
  animation: pulse 2s var(--animation-timing) infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.icon-spin {
  animation: spin 2s linear infinite;
}

/* 滚动提示动画 */
.scroll-indicator {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20px);
  }
  60% {
    transform: translateY(-10px);
  }
}

/* 文字渐变效果 */
.text-gradient {
  background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 200% 200%;
  animation: gradientBG 5s ease infinite;
}

/* 阴影动画 */
.shadow-pulse {
  animation: shadowPulse 2s infinite;
}

@keyframes shadowPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 46, 93, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(0, 46, 93, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 46, 93, 0);
  }
} 