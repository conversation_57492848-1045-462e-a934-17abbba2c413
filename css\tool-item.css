/* 工具项链接样式 */
.tool-item-link {
  display: block;
  text-decoration: none;
  color: inherit;
  height: 100%;
  transition: all 0.3s ease;
  position: relative;
  margin-bottom: 10px;
}

.tool-item-link:hover {
  text-decoration: none;
  color: inherit;
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(11, 118, 206, 0.1);
}

/* 确保工具项在链接内部正确显示 */
.tool-item-link .tool-item {
  height: 100%;
  cursor: pointer;
  padding: 25px 20px;
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 0 5px 15px rgba(0,0,0,0.05);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

/* 移除工具项自身的悬停效果，因为现在由链接控制 */
.tool-item-link:hover .tool-item {
  transform: none;
}

/* 添加悬停时的视觉反馈 */
.tool-item-link:after {
  content: '';
  position: absolute;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 2px;
  background-color: #fff;
  transition: all 0.3s ease;
}

.tool-item-link:hover:after {
  width: 30%;
}

.tool-icon-container {
  margin-bottom: 15px;
  width: 70px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: rgba(11, 118, 206, 0.1);
  transition: all 0.3s ease;
}

.tool-icon {
  font-size: 2rem;
  color: var(--primary-color);
}

.tool-name {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 10px;
  color: var(--secondary-color);
}

.tool-description {
  font-size: 0.95rem;
  color: var(--gray);
  margin-bottom: 0;
}

.tool-item-link:hover .tool-icon-container {
  background-color: var(--primary-color);
}

.tool-item-link:hover .tool-icon {
  color: #fff;
}

@media (max-width: 991.98px) {
  .tool-icon-container {
    width: 60px;
    height: 60px;
  }
  
  .tool-icon {
    font-size: 1.75rem;
  }
  
  .tool-name {
    font-size: 1.2rem;
  }
}

@media (max-width: 767.98px) {
  .tool-item-link .tool-item {
    padding: 20px 15px;
  }
}